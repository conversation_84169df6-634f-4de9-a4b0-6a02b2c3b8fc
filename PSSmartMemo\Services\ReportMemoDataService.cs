using Microsoft.EntityFrameworkCore;
using PSSmartMemo.DTO;
using PSSmartMemo.Model;

namespace PSSmartMemo.Services;

public class ReportMemoDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    public async Task<List<ReportMemoDto>> GetUserAssignedMemosAsync(string userId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        
        // Get user's assigned template IDs
        var assignedTemplateIds = await (from rut in dc.ReportUsersMemoTemplates
                                        where rut.UserId == userId
                                        select rut.MemoTemplateId).ToListAsync();
        
        if (!assignedTemplateIds.Any())
            return new List<ReportMemoDto>();

        // Get memos based on assigned templates
        var memos = await (from m in dc.Memos
                          join mt in dc.MemoTemplates on m.MemoTemplateId equals mt.MemoTemplateId
                          join mtype in dc.MemoTypes on mt.MemoTypeId equals mtype.MemoTypeId
                          where assignedTemplateIds.Contains(mt.MemoTemplateId) &&
                                m.MemoIsDel == false &&
                                m.MemoIsActive == true
                          orderby m.MemoCreatedDate descending
                          select new ReportMemoDto
                          {
                              MemoId = m.MemoId,
                              MemoCode = m.MemoCode,
                              MemoTitle = m.MemoTitle,
                              MemoStatus = m.MemoStatus,
                              MemoCreatedDate = m.MemoCreatedDate,
                              MemoCreatedBy = m.MemoCreatedBy,
                              MemoTemplateTitle = mt.MemoTemplateTitle,
                              MemoTypeTitle = mtype.MemoTypeName,
                              MemoTypeCode = mtype.MemoTypeCode,
                              Department = m.MemoDepartment ?? "",
                              Division = m.MemoDivision ?? "",
                              LastAction = m.LastAction ?? "",
                              CurrentStatus = m.MemoStatus ?? ""
                          }).ToListAsync();

        // Get last action dates for each memo
        foreach (var memo in memos)
        {
            var lastLog = await (from log in dc.MemoApprovalLogs
                               where log.MemoId == memo.MemoId
                               orderby log.ActionDate descending
                               select log.ActionDate).FirstOrDefaultAsync();
            
            memo.LastActionDate = lastLog;
        }

        return memos;
    }

    public async Task<List<ReportMemoDto>> GetUserAssignedMemosByTemplateAsync(string userId, int templateId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        
        // Verify user has access to this template
        var hasAccess = await dc.ReportUsersMemoTemplates
            .AnyAsync(rut => rut.UserId == userId && rut.MemoTemplateId == templateId);
        
        if (!hasAccess)
            return new List<ReportMemoDto>();

        // Get memos for specific template
        var memos = await (from m in dc.Memos
                          join mt in dc.MemoTemplates on m.MemoTemplateId equals mt.MemoTemplateId
                          join mtype in dc.MemoTypes on mt.MemoTypeId equals mtype.MemoTypeId
                          where m.MemoTemplateId == templateId &&
                                m.MemoIsDel == false &&
                                m.MemoIsActive == true
                          orderby m.MemoCreatedDate descending
                          select new ReportMemoDto
                          {
                              MemoId = m.MemoId,
                              MemoCode = m.MemoCode,
                              MemoTitle = m.MemoTitle,
                              MemoStatus = m.MemoStatus,
                              MemoCreatedDate = m.MemoCreatedDate,
                              MemoCreatedBy = m.MemoCreatedBy,
                              MemoTemplateTitle = mt.MemoTemplateTitle,
                              MemoTypeTitle = mtype.MemoTypeName,
                              MemoTypeCode = mtype.MemoTypeCode,
                              Department = m.MemoDepartment ?? "",
                              Division = m.MemoDivision ?? "",
                              LastAction = m.LastAction ?? "",
                              CurrentStatus = m.MemoStatus ?? ""
                          }).ToListAsync();

        // Get last action dates for each memo
        foreach (var memo in memos)
        {
            var lastLog = await (from log in dc.MemoApprovalLogs
                               where log.MemoId == memo.MemoId
                               orderby log.ActionDate descending
                               select log.ActionDate).FirstOrDefaultAsync();
            
            memo.LastActionDate = lastLog;
        }

        return memos;
    }

    public async Task<List<MemoTemplateSelectionDto>> GetUserAssignedTemplatesAsync(string userId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        
        var templates = await (from rut in dc.ReportUsersMemoTemplates
                              join mt in dc.MemoTemplates on rut.MemoTemplateId equals mt.MemoTemplateId
                              join mtype in dc.MemoTypes on mt.MemoTypeId equals mtype.MemoTypeId
                              where rut.UserId == userId &&
                                    mt.MemoTemplateIsActive == true &&
                                    mt.MemoTemplateIsDel == false
                              orderby mt.MemoTemplateTitle
                              select new MemoTemplateSelectionDto
                              {
                                  MemoTemplateId = mt.MemoTemplateId,
                                  MemoTemplateTitle = mt.MemoTemplateTitle,
                                  MemoTypeTitle = mtype.MemoTypeName,
                                  IsActive = mt.MemoTemplateIsActive,
                                  IsSelected = false
                              }).ToListAsync();
        
        return templates;
    }

    public async Task<MemoDetailDto?> GetMemoDetailAsync(int memoId, string userId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        
        // First verify user has access to this memo through their assigned templates
        var hasAccess = await (from m in dc.Memos
                              join rut in dc.ReportUsersMemoTemplates on m.MemoTemplateId equals rut.MemoTemplateId
                              where m.MemoId == memoId && rut.UserId == userId
                              select m).AnyAsync();
        
        if (!hasAccess)
            return null;

        // Get memo details
        var memo = await (from m in dc.Memos
                         join mt in dc.MemoTemplates on m.MemoTemplateId equals mt.MemoTemplateId
                         where m.MemoId == memoId
                         select new MemoDetailDto
                         {
                             MemoId = m.MemoId,
                             Title = m.MemoTitle,
                             MemoTemplateId = m.MemoTemplateId,
                             Template = mt.MemoTemplateTitle,
                             MaxApprover = mt.MemoTemplateApproverCountAllowed
                         }).FirstOrDefaultAsync();

        if (memo == null)
            return null;

        // Get memo sections
        var sections = await (from ms in dc.MemoSections
                              where ms.MemoId == memoId && !ms.MemoSectionIgnored
                              orderby ms.MemoSectionSortOrder
                              select new
                              {
                                  ms.MemoSectionId,
                                  ms.MemoId,
                                  ms.MemoSectionTitle,
                                  ms.MemoSectionContentHtml,
                                  ms.MemoSectionSortOrder,
                                  ms.MemoIsEncrypted
                              }).ToListAsync();

        memo.Sections = sections.Select(ms =>
        {
            var section = new MemoSectionDto
            {
                Id = ms.MemoSectionId,
                MemoId = ms.MemoId,
                Section = ms.MemoSectionTitle,
                SectionSortOrder = ms.MemoSectionSortOrder ?? 0,
                TemplateSectionTitle = ms.MemoSectionTitle,
                MemoIsEncrypted = ms.MemoIsEncrypted
            };

            // Handle encrypted content
            section.SetEncryptedContent(ms.MemoSectionContentHtml, ms.MemoIsEncrypted);

            return section;
        }).ToList();

        // Get memo attachments
        memo.Attachments = await (from ma in dc.MemoAttachments
                                 where ma.MemoId == memoId && ma.MemoAttachmentIsDel == false
                                 select new MemoAttachmentDto
                                 {
                                     Name = ma.MemoAttachmentDocName ?? ma.MemoAttachmentTitle ?? "",
                                     Type = ma.MemoAttachmentDocType ?? "",
                                     Path = ma.MemoAttachmentFilePath ?? "",
                                     Size = ((int)(ma.MemoAttachmentDocSizeMb ?? 0)).ToString(),
                                     Description = ma.MemoAttachmentDescription ?? "",
                                     MemoId = ma.MemoId ?? 0
                                 }).ToListAsync();

        // Get memo approvers
        memo.ApproverList = await (from ma in dc.MemoApprovers
                                  where ma.MemoId == memoId && ma.MemoApproverIsDel == false
                                  orderby ma.MemoApproverSortOrder
                                  select new MemoApproverDto
                                  {
                                      UserId = ma.MemoApproverUserId,
                                      User = ma.MemoApproverUserName,
                                      Title = ma.MemoApproverTitle,
                                      Email = ma.MemoApproverUserEmail,
                                      SortOrder = ma.MemoApproverSortOrder ?? 0
                                  }).ToListAsync();

        return memo;
    }
}
